# Save this as your_backtest_script.py

import backtrader as bt
import datetime
# import pytz # You'll likely need this for robust timezone handling later

# --- Step 3: Define Your Data Feed in Backtrader ---

# *** IMPORTANT NOTE: YahooFinanceData for EURUSD=X provides DAILY data. ***
# *** The "Tokyo Sweep Reversal Strategy" requires INTRADAY (15M, 5M) data. ***
# *** You will need to acquire separate intraday data feeds for this strategy to work. ***
# *** The 'data' below is kept for reference but will NOT support the intraday logic. ***

ticker_symbol = 'EURUSD=X'
start_date = datetime.datetime(2023, 1, 1)
end_date = datetime.datetime.now()

## This data feed fetches DAILY data.
#data_daily = bt.feeds.YahooFinanceData(
#    dataname=ticker_symbol,
#    fromdate=start_date,
#    todate=end_date
#)
#
#print(f"Daily Data feed for {ticker_symbol} from {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')} defined.")

# --- Step 4: Create Your Strategy Class (Tokyo Sweep Reversal Strategy) ---
class TokyoSweepReversalStrategy(bt.Strategy):
    # Define strategy parameters
    params = (
        ('tokyo_start_hour_gmt', 0),    # 12 AM GMT
        ('tokyo_end_hour_gmt', 6),      # 6 AM GMT
        ('london_entry_start_hour_gmt', 6), # 6 AM GMT
        ('london_entry_end_hour_gmt', 8.5), # 8:30 AM GMT (8.5 means 8 hours 30 mins)
        ('stake', 10),                  # Number of units to trade
    )

    def __init__(self):
        # Get reference to both data feeds
        self.data0 = self.datas[1]  # 5M data (primary for entry)
        self.data1 = self.datas[0]  # 15M data (for sweep detection)
        
        # Store lines for easier access
        self.dataclose = self.data0.close
        self.datahigh = self.data0.high
        self.datalow = self.data0.low

        # To keep track of pending orders
        self.order = None

        # Variables to track Tokyo session high/low for the previous completed Tokyo session
        self.tokyo_session_high = None
        self.tokyo_session_low = None
        self.last_tokyo_day = None # To track when a new Tokyo session starts

        # Flag to indicate if a sweep has been detected
        self.sweep_detected = False
        self.sweep_direction = None # 'up' or 'down'

        self.log('Strategy Initialized: Tokyo Sweep Reversal')

    def log(self, txt, dt=None):
        ''' Logging function for this strategy'''
        dt = dt or self.datas[0].datetime.datetime(0) # Use datetime.datetime(0) for full timestamp
        print(f'{dt.isoformat()}, {txt}')

    def notify_order(self, order):
        if order.status in [order.Submitted, order.Accepted]:
            return # Order submitted/accepted - Nothing to do yet

        if order.status in [order.Completed]:
            if order.isbuy():
                self.log(
                    f'BUY EXECUTED, Price: {order.executed.price:.4f}, Cost: {order.executed.value:.4f}, Comm: {order.executed.comm:.4f}'
                )
            elif order.issell():
                self.log(
                    f'SELL EXECUTED, Price: {order.executed.price:.4f}, Cost: {order.executed.value:.4f}, Comm: {order.executed.comm:.4f}'
                )
        elif order.status in [order.Canceled, order.Margin, order.Rejected]:
            self.log('Order Canceled/Margin/Rejected')

        self.order = None # Reset pending order flag

    def notify_trade(self, trade):
        if not trade.isclosed:
            return # Trade is still open

        self.log(f'TRADE CLOSED, GROSS P&L: {trade.pnl:.4f}, NET P&L: {trade.pnlcomm:.4f}')

    def next(self):
        # Access the current datetime for the primary (e.g., 5M) timeframe
        current_dt = self.data0.datetime.datetime(0)
        current_hour_gmt = current_dt.hour
        current_minute_gmt = current_dt.minute

        self.log(f'Current Bar - Close: {self.dataclose[0]:.4f}')

        # --- 1. Tokyo Session High/Low Tracking (Conceptual - requires intraday data) ---
        # This logic assumes the primary data feed (self.data0) is the 5M timeframe
        # And we would have another data feed (self.data1) for 15M if needed.

        # We need to know if this is the start of a new day for Tokyo session tracking
        if self.last_tokyo_day is None or current_dt.day != self.last_tokyo_day:
            self.log(f'New Day: {current_dt.isoformat()}')
            # Reset for a new day
            self.tokyo_session_high = -float('inf')
            self.tokyo_session_low = float('inf')
            self.sweep_detected = False
            self.sweep_direction = None
            self.last_tokyo_day = current_dt.day

        # If within Tokyo session (12 AM - 6 AM GMT)
        if self.p.tokyo_start_hour_gmt <= current_hour_gmt < self.p.tokyo_end_hour_gmt:
            # Update Tokyo session high/low
            self.tokyo_session_high = max(self.tokyo_session_high, self.datahigh[0])
            self.tokyo_session_low = min(self.tokyo_session_low, self.datalow[0])
            # self.log(f'Tokyo Session: H={self.tokyo_session_high:.4f}, L={self.tokyo_session_low:.4f}')
            return # Do not trade during Tokyo session, just track high/low

        # --- 2. Sweep Detection (Conceptual - needs 15M data or logic on 5M) ---
        # This part would ideally use data from the 15M timeframe if available.
        # For demonstration, we'll use the primary data0 (e.g., 5M) to illustrate.
        if not self.sweep_detected and self.tokyo_session_high is not None and self.tokyo_session_low is not None:
            if self.datahigh[0] > self.tokyo_session_high:
                self.log(f'Tokyo High Swept! Price: {self.datahigh[0]:.4f} > Tokyo High: {self.tokyo_session_high:.4f}')
                self.sweep_detected = True
                self.sweep_direction = 'up'
            elif self.datalow[0] < self.tokyo_session_low:
                self.log(f'Tokyo Low Swept! Price: {self.datalow[0]:.4f} < Tokyo Low: {self.tokyo_session_low:.4f}')
                self.sweep_detected = True
                self.sweep_direction = 'down'

        # --- 3. Entry Logic (Conceptual - London Open window, 5M data) ---
        # Ensure no order is pending
        if self.order:
            return

        # Check if we are within the London trading window
        if self.p.london_entry_start_hour_gmt <= current_hour_gmt < self.p.london_entry_end_hour_gmt:
            if self.sweep_detected:
                if self.sweep_direction == 'up' and not self.position:
                    # Logic for rejection after sweeping Tokyo High (looking for sell signal)
                    # Example: current bar closes below open (bearish), indicating rejection
                    if self.dataclose[0] < self.datas[0].open[0]: # Simple bearish rejection candle
                        self.log(f'SELL CREATE (Tokyo High Sweep Rejection), {self.dataclose[0]:.4f}')
                        self.order = self.sell(size=self.p.stake)

                elif self.sweep_direction == 'down' and not self.position:
                    # Logic for rejection after sweeping Tokyo Low (looking for buy signal)
                    # Example: current bar closes above open (bullish), indicating rejection
                    if self.dataclose[0] > self.datas[0].open[0]: # Simple bullish rejection candle
                        self.log(f'BUY CREATE (Tokyo Low Sweep Rejection), {self.dataclose[0]:.4f}')
                        self.order = self.buy(size=self.p.stake)

        # --- Position Management (e.g., stop loss, take profit - not in current prompt, but vital) ---
        # self.close() or specific stop/limit orders would go here
# Note: The cerebro setup and run calls are commented out for now
# as we're still focusing on getting the data feed working.

# This part would go AFTER your strategy class definition

# --- Cerebro Setup (Uncomment and Configure for Intraday Data) ---
cerebro = bt.Cerebro()

# Add your strategy
cerebro.addstrategy(TokyoSweepReversalStrategy)

# --- Configuration for your INTRADAY Data Feeds (You need to have these CSVs from Dukascopy!) ---
# Ensure these file paths are correct and the files are in your ~/zoro directory
csv_15m_path = 'eurusd_15m.csv'
csv_5m_path = 'eurusd_5m.csv'

# Define the date format for your CSV. Adjust if your CSV has a different format!
# Dukascopy usually uses 'YYYY.MM.DD HH:MM:SS' -> '%Y.%m.%d %H:%M:%S'
dt_format_str = '%d.%m.%Y %H:%M:%S GMT%z'

# Define column mapping for your CSV.
# Assuming CSV columns are: DateTime, Open, High, Low, Close, Volume
column_mapping = {
    'datetime': 0,
    'open': 1,
    'high': 2,
    'low': 3,
    'close': 4,
    'volume': 5,
    'openinterest': -1 # -1 means column not present
}

try:
    # Add the 15-minute data feed (e.g., for sweep detection)
    data_15m = bt.feeds.GenericCSVData(
        dataname=csv_15m_path,
        fromdate=datetime.datetime(2023, 1, 1), # IMPORTANT: Adjust to your data's actual start date
        todate=datetime.datetime(2025, 5, 31), # IMPORTANT: Adjust to your data's actual end date (e.g., May 31, 2025 as in screenshot)
        dtformat=dt_format_str,
        timeframe=bt.TimeFrame.Minutes,
        compression=15,
        **column_mapping, # Unpack the dictionary for column mapping
        headers=True # Set to False if your CSV has no header row
    )
    cerebro.adddata(data_15m, name='15M_Data') # Add 15M data feed

    # Add the 5-minute data feed (will be datas[0] by default in strategy as it's added last)
    data_5m = bt.feeds.GenericCSVData(
        dataname=csv_5m_path,
        fromdate=datetime.datetime(2023, 1, 1), # IMPORTANT: Adjust to your data's actual start date
        todate=datetime.datetime(2025, 5, 31), # IMPORTANT: Adjust to your data's actual end date
        dtformat=dt_format_str,
        timeframe=bt.TimeFrame.Minutes,
        compression=5,
        **column_mapping, # Unpack the dictionary for column mapping
        headers=True # Set to False if your CSV has no header row
    )
    cerebro.adddata(data_5m, name='5M_Data') # Add 5M data feed as the primary feed

    # Configure broker and initial capital
    cerebro.broker.setcash(100000.0) # Starting capital
    cerebro.broker.setcommission(commission=0.00007) # Example commission for EUR/USD (0.7 pips per standard lot round turn)
    cerebro.addsizer(bt.sizers.PercentSizer, perc=99) # Use 99% of available cash for stake

    print(f'Starting Portfolio Value: {cerebro.broker.getvalue():.2f}')

    # Run the backtest
    results = cerebro.run()

    print(f'Final Portfolio Value: {cerebro.broker.getvalue():.2f}')

    # Optional: Plot the results (requires matplotlib and a display environment)
    # cerebro.plot()

except FileNotFoundError as e:
    print(f"\nError: Data file not found. Please ensure '{e.filename}' exists in the script directory.")
    print("You need to download the intraday EUR/USD CSV data (e.g., from Dukascopy) and place it here.")
except Exception as e:
    print(f"\nAn error occurred during backtest setup or run: {e}")
